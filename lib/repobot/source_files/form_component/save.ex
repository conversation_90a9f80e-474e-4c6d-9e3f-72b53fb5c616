defmodule Repobot.SourceFiles.FormComponent.Save do
  use Repobot.Operations, type: :command

  alias <PERSON>obot.{SourceFiles, Tags}
  alias Repobot.SourceFiles.SourceFile

  schema(SourceFile)

  @impl true
  def prepare(context) do
    params = Map.get(context, :params, %{})
    action = Map.get(context, :action)
    current_user = Map.get(context, :current_user)

    # Map tags string to list of tag names
    updated_params =
      case Map.get(params, "tags") do
        tags when is_binary(tags) ->
          tag_names =
            tags
            |> String.split(",")
            |> Enum.map(&String.trim/1)
            |> Enum.reject(&(&1 == ""))

          Map.put(params, "tags", tag_names)

        _ ->
          params
      end

    # For :edit action, ensure we have the current user preloaded for tag operations
    updated_context =
      case action do
        :edit when not is_nil(current_user) ->
          # Ensure we have the current user for tag operations
          user_with_org = Repobot.Repo.preload(current_user, :default_organization)
          Map.put(context, :current_user, user_with_org)

        _ ->
          context
      end

    {:ok, Map.put(updated_context, :params, updated_params)}
  end

  @impl true
  def cast_changeset(context) do
    changeset = Map.get(context, :changeset)
    params = Map.get(context, :params, %{})
    current_user = Map.get(context, :current_user)

    # Handle tags association
    updated_changeset =
      case Map.get(params, "tags") do
        tag_names
        when is_list(tag_names) and length(tag_names) > 0 and not is_nil(current_user) ->
          tags = Tags.get_or_create_tags(tag_names, current_user)
          Ecto.Changeset.put_assoc(changeset, :tags, tags)

        _ ->
          changeset
      end

    Map.put(context, :changeset, updated_changeset)
  end

  @impl true
  def execute(context) do
    changeset = Map.get(context, :changeset)
    action = Map.get(context, :action)
    source_file = Map.get(context, :source_file)

    case action do
      :new ->
        case SourceFiles.create_source_file_from_changeset(changeset) do
          {:ok, source_file} -> {:ok, source_file}
          {:error, changeset} -> {:error, changeset}
        end

      :edit ->
        # Check if source file is read-only
        if source_file && source_file.read_only do
          {:error, "Cannot update read-only source file"}
        else
          case SourceFiles.update_source_file_from_changeset(source_file, changeset) do
            {:ok, source_file} -> {:ok, source_file}
            {:error, changeset} -> {:error, changeset}
          end
        end
    end
  end
end
