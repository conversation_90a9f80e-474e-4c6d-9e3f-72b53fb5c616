defmodule Repobot.SourceFiles.FormComponent.Save do
  use Repobot.Operations, type: :command

  alias <PERSON>obot.{SourceFiles, Tags}
  alias Repobot.SourceFiles.SourceFile

  schema(SourceFile) do
    %{
      optional(:action) => atom(),
      optional(:source_file) => SourceFile.t(),
      optional(:current_user) => any(),
      optional(:current_organization) => any()
    }
  end

  @impl true
  def prepare(%{params: params, action: action, current_user: current_user} = context) do
    # Map tags string to list of tag names
    updated_params =
      case Map.get(params, "tags") do
        tags when is_binary(tags) ->
          tag_names =
            tags
            |> String.split(",")
            |> Enum.map(&String.trim/1)
            |> Enum.reject(&(&1 == ""))

          Map.put(params, "tags", tag_names)

        _ ->
          params
      end

    # For :edit action, get tags from current user context
    updated_context =
      case action do
        :edit ->
          # Ensure we have the current user for tag operations
          user_with_org = Repobot.Repo.preload(current_user, :default_organization)
          Map.put(context, :current_user, user_with_org)

        :new ->
          context
      end

    {:ok, Map.put(updated_context, :params, updated_params)}
  end

  @impl true
  def cast_changeset(
        %{changeset: changeset, params: params, action: action, current_user: current_user} =
          context
      ) do
    # Handle tags association
    updated_changeset =
      case Map.get(params, "tags") do
        tag_names when is_list(tag_names) and length(tag_names) > 0 ->
          tags = Tags.get_or_create_tags(tag_names, current_user)
          Ecto.Changeset.put_assoc(changeset, :tags, tags)

        _ ->
          changeset
      end

    Map.put(context, :changeset, updated_changeset)
  end

  @impl true
  def execute(%{changeset: changeset, action: :new}) do
    case SourceFiles.create_source_file_from_changeset(changeset) do
      {:ok, source_file} -> {:ok, source_file}
      {:error, changeset} -> {:error, changeset}
    end
  end

  def execute(%{changeset: changeset, action: :edit, source_file: source_file}) do
    # Check if source file is read-only
    if source_file.read_only do
      {:error, "Cannot update read-only source file"}
    else
      case SourceFiles.update_source_file_from_changeset(source_file, changeset) do
        {:ok, source_file} -> {:ok, source_file}
        {:error, changeset} -> {:error, changeset}
      end
    end
  end
end
