defmodule Repobot.SourceFiles.FormComponent.SaveTest do
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  alias Repobot.SourceFiles.FormComponent.Save

  import Repobot.Test.Fixtures

  describe "Save command" do
    setup do
      user = create_user()
      organization = create_organization()

      {:ok, %{user: user, organization: organization}}
    end

    test "creates a new source file", %{user: user, organization: organization} do
      params = %{
        "name" => "test.txt",
        "content" => "Hello, World!",
        "target_path" => "test.txt",
        "tags" => "tag1, tag2",
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      context = %{
        params: params,
        action: :new,
        current_user: user,
        current_organization: organization
      }

      assert {:ok, source_file} = Save.call(context)
      assert source_file.name == "test.txt"
      assert source_file.content == "Hello, World!"
      assert source_file.target_path == "test.txt"
      assert source_file.user_id == user.id
      assert source_file.organization_id == organization.id
      assert length(source_file.tags) == 2
    end

    test "updates an existing source file", %{user: user, organization: organization} do
      source_file = create_source_file(%{
        name: "original.txt",
        content: "Original content",
        user_id: user.id,
        organization_id: organization.id
      })

      params = %{
        "name" => "updated.txt",
        "content" => "Updated content",
        "target_path" => "updated.txt",
        "tags" => "new-tag",
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      context = %{
        params: params,
        action: :edit,
        source_file: source_file,
        current_user: user,
        current_organization: organization
      }

      assert {:ok, updated_source_file} = Save.call(context)
      assert updated_source_file.name == "updated.txt"
      assert updated_source_file.content == "Updated content"
      assert updated_source_file.target_path == "updated.txt"
      assert updated_source_file.id == source_file.id
      assert length(updated_source_file.tags) == 1
    end

    test "prevents updating read-only source files", %{user: user, organization: organization} do
      source_file = create_source_file(%{
        name: "readonly.txt",
        content: "Read-only content",
        read_only: true,
        user_id: user.id,
        organization_id: organization.id
      })

      params = %{
        "name" => "should-not-update.txt",
        "content" => "Should not update",
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      context = %{
        params: params,
        action: :edit,
        source_file: source_file,
        current_user: user,
        current_organization: organization
      }

      assert {:error, error_message} = Save.call(context)
      assert error_message =~ "Cannot update read-only source file"
    end
  end
end
